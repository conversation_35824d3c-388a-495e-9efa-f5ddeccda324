/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#000000',
        link: '#D36C3C',
        'link-hover': '#e98a5e',
        highlight: '#2D4D31',
        button: '#2D4D31',
        'button-hover': '#223a24',
        'button-border': '#2D4D31',
        background: '#FFF5EA',
        header: '#ffffff',
        'agri-cream': '#FFF5EA',
        'agri-green': '#2D4D31',
      },
      animation: {
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        }
      }
    },
  },
  plugins: [],
}
