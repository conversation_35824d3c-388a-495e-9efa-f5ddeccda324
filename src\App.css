.hero__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(45, 77, 49, 0.8) 0%,
    rgba(45, 77, 49, 0.6) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
  z-index: -1;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.story-content h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--primary-text);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.story-content p {
  font-size: var(--font-size-base);
  color: rgba(0, 0, 0, 0.8);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
}

.story-content p:last-child {
  margin-bottom: 0;
}